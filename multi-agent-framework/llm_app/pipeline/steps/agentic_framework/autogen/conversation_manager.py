import asyncio
from typing import Dict, <PERSON>, AsyncGenerator, Any
from loguru import logger

from autogen_core.models import ModelFamily
from autogen_core.tools import FunctionTool
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core import SingleThreadedAgentRuntime, TopicId
from autogen_core.models import UserMessage, AssistantMessage

from llm_app.pipeline.steps.agentic_framework.autogen.models.models import UserTask
from llm_app.pipeline.steps.agentic_framework.autogen.agent.ai_agent_wrapper import AIAgentWrapper
from llm_app.pipeline.steps.agentic_framework.autogen.agent.knowledge_base_ai_agent_wrapper import (
    KnowledgeBaseAIAgentWrapper,
)

from llm_app.pipeline.steps.agentic_framework.autogen.agent.triage_agent_wrapper import TriageAgentWrapper
from llm_app.pipeline.steps.agentic_framework.autogen.agent.triage_agent_v2_wrapper import TriageAgentV2Wrapper
from llm_app.pipeline.steps.agentic_framework.autogen.agent.user_agent_wrapper import User<PERSON><PERSON><PERSON>rapper
from llm_app.pipeline.steps.agentic_framework.autogen.session.conversation_session import ConversationSession
from llm_app.pipeline.steps.agentic_framework.autogen.utils.tool_generation import resolve_tools

from llm_app.config.config_store import get_agent_config, get_roster_config


STREAM_DONE = object()


def get_model_client_for_agent(roster_config: dict, agent_type: str = "default") -> OpenAIChatCompletionClient:
    """
    Create a model client for a specific agent type based on roster configuration.

    Args:
        roster_config (dict): Roster configuration containing model settings
        agent_type (str): Type of agent (e.g., "triage_agent", "workflow_agent", "default")

    Returns:
        OpenAIChatCompletionClient: Configured model client
    """
    model_config = roster_config.get("model", {})
    model_name = model_config.get(agent_type, model_config.get("default", "gpt-4.1"))

    print(f"🤖 [MODEL_CLIENT] Creating model client for {agent_type}: {model_name}")

    # Map model names to actual model identifiers
    model_mapping = {
        "gpt-4.1": "gpt-4.1-2025-04-14",
        "gpt-4o": "gpt-4o-2024-08-06",
        "gpt-4": "gpt-4-turbo-2024-04-09"
    }

    actual_model = model_mapping.get(model_name, "gpt-4.1-2025-04-14")

    return OpenAIChatCompletionClient(
        model=actual_model,
        model_info={
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "family": ModelFamily.GPT_4O,
            "structured_output": True,
        },
    )


class ConversationManager:
    def __init__(self):
        self.sessions = {}

    async def start_session(
        self,
        conversation_id: str,
        roster_id: str,
        llm_context: Dict[str, str],
        user_context: Dict[str, Any],
        channel: str,
    ):
        response_queue = asyncio.Queue()
        runtime = SingleThreadedAgentRuntime()
        context_arguments = user_context.copy()

        # Step 3: Fetch the roster config first to get model configurations
        roster_config = get_roster_config(roster_id)

        # Init default model client
        model_client = get_model_client_for_agent(roster_config, "default")

        # Step 1: Create Session Context Object
        session_context_object = ConversationSession()

        # Step 2: Create User Agent
        user_wrapper = UserAgentWrapper(
            runtime=runtime,
            response_queue=response_queue,
            description="User agent.",
            stream_done=STREAM_DONE,
        )
        await user_wrapper.create()

        # Step 4.1: Create Triage Agent (V1 or V2 based on configuration)
        triage_agent_version = roster_config.get("use_triage_agent", "V1")

        print(f"🔍 [CONVERSATION_MANAGER] Checking triage agent version...")
        print(f"📋 [CONVERSATION_MANAGER] Roster ID: {roster_config.get('roster_id', 'Unknown')}")
        print(f"🎛️  [CONVERSATION_MANAGER] use_triage_agent setting: {triage_agent_version}")

        # Create specific model client for triage agent
        triage_model_client = get_model_client_for_agent(roster_config, "triage_agent")

        if triage_agent_version == "V2":
            print(f"🚀 [CONVERSATION_MANAGER] Using Enhanced Triage Agent V2 (Shubham's Requirements)")
            print(f"✨ [CONVERSATION_MANAGER] Features: Talk-back, Clarification, Unknown-info handling, Mid-conversation transfer")

            # Use enhanced triage agent V2 following Shubham's requirements
            triage_wrapper = TriageAgentV2Wrapper(
                config=roster_config,
                runtime=runtime,
                model_client=triage_model_client,
                response_queue=response_queue,
                user_topic_type=user_wrapper.agent_topic_type,
                toolset=[],
                delegate_tools=[],
            )
        else:
            print(f"📝 [CONVERSATION_MANAGER] Using Original Triage Agent V1 (Legacy)")

            # Use original triage agent
            triage_wrapper = TriageAgentWrapper(
                config=roster_config,
                runtime=runtime,
                model_client=triage_model_client,
                response_queue=response_queue,
                user_topic_type=user_wrapper.agent_topic_type,
                toolset=[],
                delegate_tools=[],
            )

        print(f"✅ [CONVERSATION_MANAGER] Triage agent wrapper created: {type(triage_wrapper).__name__}")

        # Step 4.2: Create Triage Agent delegate tool
        transfer_back_to_triage_tool = triage_wrapper.get_delegate_tool()

        # Step 5: Create all Other AI agents
        agent_wrappers = []
        workflows = list(roster_config.get("workflows", {}).keys())
        additional_info = {
            "avatar_name": roster_config.get("avatar_name"),
            "company_name": roster_config.get("company_name"),
            "use_case_domain": roster_config.get("use_case_domain"),
            "time_zone": roster_config.get("time_zone"),
            "llm_context": llm_context,
            "channel": channel,
        }
        for workflow_id in workflows:
            agent_cfg = get_agent_config(workflow_id)
            agent_cfg.update(additional_info)
            wrapper = AIAgentWrapper(
                config=agent_cfg,
                runtime=runtime,
                model_client=model_client,
                response_queue=response_queue,
                user_topic_type=user_wrapper.agent_topic_type,
                toolset=resolve_tools(agent_cfg["function_map"]),
                delegate_tools=[transfer_back_to_triage_tool],
            )
            # Step 5.2: Pre-generate delegate tools for all agents
            delegate_tool = wrapper.get_delegate_tool()  # create tool from config
            agent_wrappers.append((wrapper, delegate_tool))  # store both for triage use

        if roster_config.get("knowledge_base_agent", False) and roster_config.get("knowledge_base_config"):
            kb_agent_config = roster_config["knowledge_base_config"]
            kb_agent_config.update(additional_info)
            context_arguments["illuminar_config"] = kb_agent_config["illuminar_config"]
            kb_wrapper = KnowledgeBaseAIAgentWrapper(
                config=kb_agent_config,
                runtime=runtime,
                model_client=model_client,
                response_queue=response_queue,
                user_topic_type=user_wrapper.agent_topic_type,
                toolset=resolve_tools(agent_cfg["function_map"]),
                delegate_tools=[transfer_back_to_triage_tool],
            )
            kb_delegate_tool = kb_wrapper.get_delegate_tool()
            agent_wrappers.append((kb_wrapper, kb_delegate_tool))

        # Step 7: Update Triage Agent delegate tool with delegate tools from other Agents
        triage_wrapper.delegate_tools = [delegate_tool for _, delegate_tool in agent_wrappers]

        # Step 8: Create all Other AI Agents
        for wrapper, _ in agent_wrappers:
            await wrapper.create()

        # Step 9: Create Triage Agent
        await triage_wrapper.create()

        # Step 10: Prepare welcome message
        welcome_message_list = roster_config.get("welcome_message", {}).get(
            user_context.get("WelcomeMessageFlag", "default")
        )
        welcome_message = ""
        for message in welcome_message_list:
            welcome_message += message.get("message", "").format(**user_context) + " "

        # Step 11: Start the runtime and store in session
        runtime.start()
        self.sessions[conversation_id] = {
            "runtime": runtime,
            "response_queue": response_queue,
            "session_context": session_context_object,
            "route_agent": triage_wrapper.agent_topic_type,
            "context_arguments": context_arguments,
            "initial_message": welcome_message,
            "channel": channel,
        }

    async def run(self, conversation_id: str, event_template, channel: str) -> AsyncGenerator[dict, None]:
        if conversation_id not in self.sessions:
            if event_template.event_type != "initiate":
                raise ValueError(f"Session for '{conversation_id}' not found. Send an 'initiate' event first.")

        if event_template.event_type in ["text","contextkey"]:
            route_agent = await self.sessions[conversation_id]["session_context"].get_route_agent(
                self.sessions[conversation_id]["route_agent"]
            )
            if event_template.event_type == "text":
                await self.sessions[conversation_id]["session_context"]._session_context.add_message(
                    UserMessage(content=event_template.text, source="User")
                )
            elif event_template.event_type == "contextkey":
                await self.sessions[conversation_id]["session_context"]._session_context.add_message(
                    UserMessage(content=event_template.context_label, source="User")
                )
            conversation_session_context = self.sessions[conversation_id]["session_context"]
            context_arguments = self.sessions[conversation_id]["context_arguments"]
            # Determine routing based on last assistant response

            await self.sessions[conversation_id]["runtime"].publish_message(
                UserTask(
                    conversation_session_context=conversation_session_context,
                    context_arguments=context_arguments,
                ),
                topic_id=TopicId(
                    type=route_agent,
                    source=conversation_id,
                ),
            )

            # Stream responses
            while True:
                item = await self.sessions[conversation_id]["response_queue"].get()
                if item is STREAM_DONE:
                    break
                else:
                    logger.debug("response from agent:", data={"result": item})
                    yield item

        elif event_template.event_type == "initiate":
            await self.start_session(
                conversation_id,
                event_template.rosters_id,
                event_template.llm_context,
                event_template.user_context,
                channel,
            )
            yield {
                "response_type": "text",
                "text": self.sessions[conversation_id]["initial_message"],
                "template": None,
                "partial_response": False,
                "should_end_interaction": False,
                "is_forced_text_template": True,
            }

        else:
            yield {"type": "error", "text": f"Unknown event_type: {event_template.event_type}"}
