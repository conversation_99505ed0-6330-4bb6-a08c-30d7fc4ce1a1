import types
from autogen_core import TypeSubscription
from autogen_core.models import SystemMessage
from autogen_core.tools import FunctionTool

from llm_app.pipeline.steps.agentic_framework.autogen.interfaces.agent_base import AIAgent

from llm_app.pipeline.steps.agentic_framework.autogen.utils.prompt_generation import _get_triage_agent_system_prompt

class TriageAgentWrapper:
    def __init__(
        self,
        *,
        config: dict,
        runtime,
        model_client,
        response_queue,
        user_topic_type,
        toolset: list = None,
        delegate_tools: list = None,
    ):
        # Enhanced logging for V1 triage agent
        print(f"[TRIAGE_V1] Initializing Triage Agent V1")
        print(f"[TRIAGE_V1] Config keys: {list(config.keys())}")
        print(f"[TRIAGE_V1] Company: {config.get('company_name', 'Unknown')}")
        print(f"[TRIAGE_V1] Avatar: {config.get('avatar_name', 'Unknown')}")
        print(f"[TRIAGE_V1] Domain: {config.get('use_case_domain', 'Unknown')}")
        print(f"[TRIAGE_V1] Timezone: {config.get('time_zone', 'Unknown')}")
        print(f"[TRIAGE_V1] Channel Guidelines: {config.get('channel_guidelines', 'None')[:100]}...")

        # Count available workflows
        workflows = config.get('workflows', {})
        print(f"[TRIAGE_V1] Available workflows: {len(workflows)}")
        for workflow_id, workflow_info in workflows.items():
            # Handle both dict and list workflow formats
            if isinstance(workflow_info, dict):
                workflow_name = workflow_info.get('name', workflow_id)
                print(f"   [TRIAGE_V1] {workflow_id}: {workflow_name}")
            elif isinstance(workflow_info, list):
                print(f"   [TRIAGE_V1] {workflow_id}: {len(workflow_info)} workflow steps")
            else:
                print(f"   [TRIAGE_V1] {workflow_id}: {type(workflow_info).__name__}")

        self.config = config
        self.runtime = runtime
        self.model_client = model_client
        self.response_queue = response_queue
        self.user_topic_type = user_topic_type
        self.toolset = toolset or []
        self.delegate_tools = delegate_tools or []

        self.agent_topic_type = "Triage_Agent"

        # Generate system prompt with logging
        print(f"[TRIAGE_V1] Generating V1 system prompt")
        system_prompt = _get_triage_agent_system_prompt(config.copy())
        print(f"[TRIAGE_V1] System prompt generated: {len(system_prompt)} characters")
        print(f"[TRIAGE_V1] Prompt preview: {system_prompt[:200]}...")

        self.system_message = SystemMessage(content=system_prompt)
        self.description = "Responsible for routing the customer to the appropriate support agent based on the issue category."

        print(f"[TRIAGE_V1] Initialization complete")

    async def create(self):
        """Create and register the V1 triage agent."""
        print(f"[TRIAGE_V1] Creating agent with topic type: {self.agent_topic_type}")
        print(f"[TRIAGE_V1] Tools available: {len(self.toolset)}")
        print(f"[TRIAGE_V1] Delegate tools: {len(self.delegate_tools)}")
        print(f"[TRIAGE_V1] User topic type: {self.user_topic_type}")

        agent_type = await AIAgent.register(
            self.runtime,
            type=self.agent_topic_type,
            factory=lambda: AIAgent(
                description=self.description,
                system_message=self.system_message,
                model_client=self.model_client,
                tools=self.toolset,
                delegate_tools=self.delegate_tools,
                agent_topic_type=self.agent_topic_type,
                user_topic_type=self.user_topic_type,
                response_queue=self.response_queue,
            ),
        )

        print(f"[TRIAGE_V1] Agent registered successfully: {agent_type.type}")

        await self.runtime.add_subscription(
            TypeSubscription(topic_type=self.agent_topic_type, agent_type=agent_type.type)
        )

        print(f"[TRIAGE_V1] Subscription added for topic: {self.agent_topic_type}")
        print(f"[TRIAGE_V1] Agent creation complete!")

        return agent_type

    def get_delegate_tool(self):
        """Create delegate tool for routing back to this V1 triage agent."""
        print(f"[TRIAGE_V1] Creating delegate tool for {self.agent_topic_type}")

        agent_role = "Responsible for routing the customer to the appropriate support agent based on the issue category."
        agent_name = "Triage_Agent"
        agent_topic = self.agent_topic_type

        # Generate a safe function name from agent_name
        func_name = f"route_to_{agent_name.lower().replace(' ', '_')}"
        print(f"[TRIAGE_V1] Delegate function name: {func_name}")

        # Define a closure-compatible function
        def _route_fn() -> str:
            print(f"[TRIAGE_V1] Delegate tool called! Routing to: {agent_topic}")
            return agent_topic

        # Create a dynamic function with the proper closure
        code = _route_fn.__code__
        globals_dict = globals()
        name = func_name
        argdefs = _route_fn.__defaults__
        closure = _route_fn.__closure__  # This is what was missing

        # Construct the new function
        route_fn = types.FunctionType(code, globals_dict, name, argdefs, closure)

        tool_description = (
            f"Use this tool if the customer is discussing anything related to: {agent_role}.\n"
            f"This will route the conversation to **{agent_name}**."
        )
        print(f"[TRIAGE_V1] Tool description: {tool_description[:100]}...")

        print(f"[TRIAGE_V1] Delegate tool created successfully")

        return FunctionTool(
            route_fn,
            description=tool_description,
        )
