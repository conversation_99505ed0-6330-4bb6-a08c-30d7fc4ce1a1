# llm_app/pipeline/models/data.py

from pydantic import BaseModel, TypeAdapter
from typing import Any, Dict

from llm_app.pipeline.models.event import PipelineEvent, EventTemplate
from llm_app.pipeline.models.state import PipelineState, RequestNormalizerState


class PipelineData(BaseModel):
    state: PipelineState
    event: PipelineEvent

    def get(self, key: str, default: Any = None) -> Any:
        return getattr(self.state, key, getattr(self.event, key, default))

    def __getitem__(self, key: str) -> Any:
        return self.get(key)

    def __setitem__(self, key: str, value: Any) -> None:
        if hasattr(self.state, key):
            setattr(self.state, key, value)
        elif hasattr(self.event, key):
            setattr(self.event, key, value)
        else:
            raise KeyError(f"'{key}' is not a valid key in PipelineData")

    def dict(self, **kwargs) -> Dict[str, Any]:
        return {
            "state": self.state.model_dump(**kwargs),
            "event": self.event.model_dump(**kwargs),
        }

    # Built-in adapter to convert raw input into PipelineData
    @classmethod
    def from_payload(cls, raw_payload: Dict[str, Any]) -> "PipelineData":
        """Builds a PipelineData instance from incoming JSON payload."""

        raw_event = raw_payload.copy()
        incoming_event = raw_event.get("incoming_events", [])[0]
        # Extract template and build appropriate event_template class
        event_template_raw = incoming_event.get("event_template", {})
        event_template = TypeAdapter(EventTemplate).validate_python(event_template_raw)
        # Wrap event
        event = PipelineEvent(
            conversation_id=raw_event.get("user_info").get("user_id").get("id"),
            raw_event=raw_payload,
            event_template=event_template,
            channel=raw_event.get("user_info").get("user_id").get("id_resource"),
        )

        # Default to initial RequestNormalizer state
        state = RequestNormalizerState(
            state_type="request_normalizer",
            normalized_input={
                "user_id": incoming_event.get("event_user", {}).get("user_id"),
                "text": event_template_raw.get("text") or event_template_raw.get("context_label"),
            },
        )

        return cls(state=state, event=event)
